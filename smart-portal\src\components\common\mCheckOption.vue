<template>
  <div class="multi-checkbox-group">
    <!-- 循环渲染多选复选框 -->
    <label 
      class="multi-checkbox"
      v-for="(item, index) in options"
      :key="index"
      :class="{ 'multi-checkbox--disabled': item.disabled }"
    >
      <!-- 原生复选框（视觉隐藏，保留语义） -->
      <input
        type="checkbox"
        class="multi-checkbox__input"
        :name="groupName"
        :value="item.value"
        :checked="isSelected(item.value)"
        :disabled="item.disabled"
        @change="handleCheckboxChange(item.value)"
      >
      <!-- 自定义复选框样式 -->
      <span class="multi-checkbox__inner">
        <!-- 选中状态的对勾图标 -->
        <i class="multi-checkbox__icon" v-if="isSelected(item.value)">✓</i>
      </span>
      <!-- 复选框文本 -->
      <span class="multi-checkbox__label">{{ item.label }}</span>
    </label>
  </div>
</template>

<script setup>'vue';
const props = defineProps({
  /** 双向绑定值（v-model），存储已选中项的value数组 */
  modelValue: {
    type: Array,
    required: true,
    // 验证：确保是数组，且元素为字符串/数字/布尔值
    validator: (val) => {
      return val.every(item => 
        typeof item === 'string' || typeof item === 'number' || typeof item === 'boolean'
      );
    },
    default: () => [] // 默认空数组（未选中任何项）
  },
  /** 选项配置数组 */
  options: {
    type: Array,
    required: true,
    // 验证options格式：必须包含label和value，disabled可选且为布尔值
    validator: (val) => {
      return val.every(item => 
        'label' in item && 'value' in item && 
        (item.disabled === undefined || typeof item.disabled === 'boolean')
      );
    }
  },
  /** 复选框组名称（区分不同组，避免原生状态冲突） */
  name: {
    type: String,
    default: 'multi-checkbox-group'
  }
});

// 定义事件：更新v-model + 选中变化回调（均传递选中值数组）
const emit = defineEmits(['update:modelValue', 'change']);

// 计算组名（确保唯一性，避免多组共存时原生状态干扰）
const groupName = computed(() => {
  return props.name || `multi-checkbox-${Date.now()}`;
});

/** 判断当前选项是否已选中 */
const isSelected = (value) => {
  // 数组包含判断（处理值类型一致的情况）
  return props.modelValue.includes(value);
};

/** 处理复选框选中/取消事件（核心多选逻辑） */
const handleCheckboxChange = (currentValue) => {
  // 复制原选中数组（避免直接修改props，符合Vue单向数据流）
  const newSelectedArr = [...props.modelValue];
  
  // 逻辑：已选中则移除（取消），未选中则添加（选中）
  const valueIndex = newSelectedArr.findIndex(item => item === currentValue);
  if (valueIndex > -1) {
    newSelectedArr.splice(valueIndex, 1); // 取消选中
  } else {
    newSelectedArr.push(currentValue); // 选中
  }

  // 触发事件：传递完整的选中值数组（供父组件同步）
  emit('update:modelValue', newSelectedArr);
  emit('change', newSelectedArr);
};
</script>

<style lang="scss" scoped>
// 基础SCSS变量：仅定义核心样式值，避免复杂计算
$cb-size: .25rem;
$cb-border-color: #dcdfe6;
$cb-active-color: #EEAF00;
$cb-disabled-color: #c0c4cc;
$cb-hover-bg: rgba(255,255,255, 0.8);
$label-color: #1D1D1D;
$active-label-color: #1D1D1D;
$option-margin: .2rem;
$inner-margin: .075rem;
$border-radius: .05rem;
$icon-size: .15rem;

// 复选框组容器：基础Flex布局
.multi-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  // margin: 0 -$option-margin / 2; // 抵消子元素margin，避免整体偏移
}

// 单个复选框容器：基础嵌套
.multi-checkbox {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  margin: 0px  .75rem  0px 0px;
  transition: color 0.2s ease;

  // 原生复选框：视觉隐藏（基础定位）
  .multi-checkbox__input {
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
    margin: 0;
    padding: 0;
    pointer-events: none;
  }

  // 自定义复选框样式：层级清晰
  .multi-checkbox__inner {
    width: $cb-size;
    height: $cb-size;
    border: 1px solid #737373;
    border-radius: $border-radius;
    background-color: #EEEEEE;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    margin-right: $inner-margin; // 与文本间距（基础margin）
  }

  // 选中状态：基础&连接符
  .multi-checkbox__input:checked + .multi-checkbox__inner {
    background-color: $cb-active-color;
    border-color: $cb-active-color;
  }

  // 禁用状态：基础嵌套
  &.multi-checkbox--disabled {
    cursor: not-allowed;

    .multi-checkbox__inner {
      background-color: #f5f7fa;
      border-color: $cb-disabled-color;
    }

    .multi-checkbox__label {
      color: $cb-disabled-color;
    }
  }

  // Hover状态：基础&连接符
  &:not(.multi-checkbox--disabled):hover .multi-checkbox__inner {
    border-color: $cb-active-color;
    // background-color: $cb-hover-bg;
  }

  // 选中对勾：基础样式
  .multi-checkbox__icon {
    color: #fff;
    font-size: $icon-size;
    font-style: normal;
  }

  // 文本样式：基础属性
  .multi-checkbox__label {
    color: $label-color;
    font-size: 14px;
    transition: color 0.2s ease;
  }

  // 选中文本变色：基础兄弟选择器
  .multi-checkbox__input:checked ~ .multi-checkbox__label {
    color: $active-label-color;
  }
}
</style>
