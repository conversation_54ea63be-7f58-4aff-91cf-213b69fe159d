<template>
  <div class="my-radio-group">
    <!-- 循环生成Radio选项 -->
    <label 
      class="my-radio"
      v-for="(item, index) in options"
      :key="index"
      :class="{ 'my-radio--disabled': item.disabled }"
    >
      <!-- 原生Radio（隐藏） -->
      <input
        type="radio"
        class="my-radio__input"
        :name="radioName"
        :value="item.value"
        :checked="modelValue === item.value"
        :disabled="item.disabled"
        @change="handleRadioChange(item.value)"
      >
      <!-- 自定义Radio样式 -->
      <span class="my-radio__inner"></span>
      <!-- Radio文本 -->
      <span class="my-radio__label">{{ item.label }}</span>
    </label>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// 定义组件Props
const props = defineProps({
  /** 绑定值（v-model） */
  modelValue: {
    type: [String, Number, Boolean],
    required: true
  },
  /** 选项配置数组 */
  options: {
    type: Array,
    required: true,
    validator: (val) => {
      // 验证options格式
      return val.every(item => 
        'label' in item && 'value' in item && 
        (item.disabled === undefined || typeof item.disabled === 'boolean')
      );
    }
  },
  /** Radio组名（可选，用于区分不同组） */
  name: {
    type: String,
    default: 'my-radio-group'
  }
});

// 定义事件（更新v-model）
const emits = defineEmits(['update:modelValue', 'change']);

// 计算Radio组名（确保唯一性）
const radioName = computed(() => {
  return props.name || `my-radio-${Date.now()}`;
});

/** 处理Radio选中事件 */
const handleRadioChange = (value) => {
  // 更新v-model绑定值
  emits('update:modelValue', value);
  // 触发change事件（传递当前选中值）
  emits('change', value);
};
</script>

<style lang="scss" scoped>
// 基础变量（可根据需求调整）
$radio-size: .25rem; // Radio按钮大小
$radio-color: #dcdfe6; // 未选中边框颜色
$radio-active-color: #eeaf00; // 选中颜色（Element UI主题色）
$radio-disabled-color: #c0c4cc; // 禁用颜色
$radio-spacing: .75rem; // 选项间距
$border-radius: 50%; // 圆形边框

.my-radio-group {
  display: flex;
  align-items: center;
  gap: $radio-spacing; // 选项之间的间距
  flex-wrap: wrap; // 支持换行
}

.my-radio {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  position: relative;
  gap: 6px; // Radio与文本间距

  // 隐藏原生Radio
  .my-radio__input {
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
    margin: 0;
    padding: 0;
  }

  // 自定义Radio按钮
  .my-radio__inner {
    width: $radio-size;
    height: $radio-size;
    border: 1px solid #737373;
    background-color: #eee;
    border-radius: $border-radius;
    position: relative;
    transition: all 0.2s ease;
  }

  // 选中状态样式
  .my-radio__input:checked + .my-radio__inner {
    border-color: $radio-active-color;
    background-color: $radio-active-color;

    // 选中后的小圆点
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: .1rem;
      height: .1rem;
      border-radius: $border-radius;
      background-color: #fff;
    }
  }

  // Hover状态
  &:not(.my-radio--disabled):hover .my-radio__inner {
    border-color: $radio-active-color;
  }

  // Radio文本样式
  .my-radio__label {
    color: #606266;
    font-size: 14px;
    transition: color 0.2s ease;
  }

  // 选中时文本颜色
  .my-radio__input:checked ~ .my-radio__label {
    color: #2b3136;
  }

  // 禁用状态样式
  &.my-radio--disabled {
    cursor: not-allowed;

    .my-radio__inner {
      border-color: $radio-disabled-color;
      background-color: #f5f7fa;
    }

    .my-radio__label {
      color: $radio-disabled-color;
    }
  }

  // 禁用状态下的选中样式
  &.my-radio--disabled .my-radio__input:checked + .my-radio__inner {
    background-color: $radio-disabled-color;
    border-color: $radio-disabled-color;
  }
}
</style>
